import json
from langchain.tools import Tool
from typing import Dict, List, Any, Optional

class DeviceRecommender:
    """
    A tool that suggests connected health devices based on missing or abnormal vitals.
    """

    def __init__(self):
        # Define mappings between vital signs and recommended devices
        self.vital_device_mapping = {
            "Glucose": {
                "devices": [
                    {
                        "name": "Continuous Glucose Monitor (CGM)",
                        "description": "Tracks glucose levels throughout the day and night, providing real-time data and alerts for high or low levels.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$200-$400",
                        "recommended_brands": ["Dexcom G6", "FreeStyle Libre", "Medtronic Guardian"]
                    },
                    {
                        "name": "Smart Glucose Meter",
                        "description": "Traditional glucose meter with digital connectivity to track readings over time.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$30-$100",
                        "recommended_brands": ["OneTouch Verio Flex", "Contour Next One", "Accu-Chek Guide"]
                    }
                ],
                "abnormal_threshold": {"low": 70, "high": 100}
            },
            "SpO2": {
                "devices": [
                    {
                        "name": "Pulse Oximeter",
                        "description": "Measures blood oxygen saturation levels and pulse rate.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$30-$100",
                        "recommended_brands": ["Wellue O2Ring", "Masimo MightySat", "Nonin Connect"]
                    }
                ],
                "abnormal_threshold": {"low": 95, "high": 100}
            },
            "ECG (Heart Rate)": {
                "devices": [
                    {
                        "name": "ECG Monitor",
                        "description": "Records electrical activity of the heart to detect irregularities.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$100-$300",
                        "recommended_brands": ["AliveCor KardiaMobile", "Withings Move ECG", "Apple Watch Series"]
                    },
                    {
                        "name": "Heart Rate Monitor",
                        "description": "Tracks heart rate during rest and activity.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$50-$150",
                        "recommended_brands": ["Polar H10", "Garmin HRM-Pro", "Wahoo TICKR X"]
                    }
                ],
                "abnormal_threshold": {"low": 60, "high": 100}
            },
            "Blood Pressure": {
                "devices": [
                    {
                        "name": "Smart Blood Pressure Monitor",
                        "description": "Measures systolic and diastolic blood pressure with digital tracking.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$50-$150",
                        "recommended_brands": ["Omron Platinum", "Withings BPM Connect", "QardioArm"]
                    }
                ],
                "abnormal_threshold": {
                    "systolic": {"low": 90, "high": 120},
                    "diastolic": {"low": 60, "high": 80}
                }
            },
            "Weight (BMI)": {
                "devices": [
                    {
                        "name": "Smart Scale",
                        "description": "Measures weight, BMI, body fat percentage, and other metrics.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$50-$150",
                        "recommended_brands": ["Withings Body+", "Eufy Smart Scale P1", "Fitbit Aria Air"]
                    }
                ],
                "abnormal_threshold": {"low": 18.5, "high": 24.9}
            },
            "Temperature": {
                "devices": [
                    {
                        "name": "Smart Thermometer",
                        "description": "Measures body temperature with digital tracking.",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$30-$100",
                        "recommended_brands": ["Kinsa Smart Thermometer", "Withings Thermo", "Tempdrop"]
                    }
                ],
                "abnormal_threshold": {"low": 36.5, "high": 37.5}
            },
            "Fev": {
                "devices": [
                    {
                        "name": "Digital Spirometer",
                        "description": "Measures lung function including FEV (Forced Expiratory Volume).",
                        "connectivity": "Bluetooth/App",
                        "price_range": "$100-$300",
                        "recommended_brands": ["Air Smart Spirometer", "NuvoAir", "Spirohome"]
                    }
                ],
                "abnormal_threshold": {"low": 80, "high": 120}
            }
        }

        # Define multi-vital devices that can track multiple metrics
        self.multi_vital_devices = [
            {
                "name": "Smartwatch with Health Tracking",
                "description": "Tracks multiple health metrics including heart rate, SpO2, ECG, sleep, and activity.",
                "tracked_vitals": ["ECG (Heart Rate)", "SpO2", "Activity"],
                "connectivity": "Bluetooth/App",
                "price_range": "$200-$500",
                "recommended_brands": ["Apple Watch", "Samsung Galaxy Watch", "Fitbit Sense", "Garmin Venu"]
            },
            {
                "name": "Health Monitoring Station",
                "description": "All-in-one device that can measure multiple vital signs including blood pressure, temperature, ECG, and SpO2.",
                "tracked_vitals": ["Blood Pressure", "Temperature", "ECG (Heart Rate)", "SpO2"],
                "connectivity": "Bluetooth/App/WiFi",
                "price_range": "$300-$800",
                "recommended_brands": ["Withings Health Station", "TytoHome", "MedWand"]
            }
        ]

    def recommend_devices(self, health_data: Dict[str, Any], test_type: Optional[str] = None) -> Dict:
        """
        Analyze health data and recommend appropriate connected health devices.

        Args:
            health_data: Dictionary containing health metrics and their values
            test_type: Optional type of test (vital_signs, kidney_function, lipid_profile, etc.)

        Returns:
            Dictionary with device recommendations
        """
        missing_vitals = []
        abnormal_vitals = []
        recommended_devices = []
        multi_device_recommendations = []
        test_specific_devices = []

        # Define test-specific device recommendations
        test_specific_device_mapping = {
            "vital_signs": [
                {
                    "name": "Smart Vital Signs Monitor",
                    "description": "All-in-one device that tracks blood pressure, heart rate, temperature, and SpO2.",
                    "connectivity": "Bluetooth/App/WiFi",
                    "price_range": "$150-$300",
                    "recommended_brands": ["Withings BPM Core", "QardioCore", "Biobeat"]
                },
                {
                    "name": "Wireless Blood Pressure Monitor",
                    "description": "Tracks blood pressure and heart rate with trend analysis and irregular heartbeat detection.",
                    "connectivity": "Bluetooth/App",
                    "price_range": "$70-$150",
                    "recommended_brands": ["Omron Platinum", "Withings BPM Connect", "QardioArm"]
                }
            ],
            "kidney_function": [
                {
                    "name": "Home Urinalysis Kit",
                    "description": "Tests for protein, glucose, ketones, and other markers in urine that can indicate kidney function.",
                    "connectivity": "App-based analysis",
                    "price_range": "$50-$200",
                    "recommended_brands": ["Healthy.io Dip.io", "Scanwell Health", "Vivoo"]
                },
                {
                    "name": "Smart Water Bottle",
                    "description": "Tracks hydration levels and reminds you to drink water, which is crucial for kidney health.",
                    "connectivity": "Bluetooth/App",
                    "price_range": "$40-$80",
                    "recommended_brands": ["HidrateSpark", "Thermos Smart Lid", "Ozmo Active"]
                }
            ],
            "lipid_profile": [
                {
                    "name": "Home Cholesterol Test Kit",
                    "description": "Measures total cholesterol, HDL, LDL, and triglycerides with app-based tracking.",
                    "connectivity": "App-based analysis",
                    "price_range": "$100-$200",
                    "recommended_brands": ["CardioChek Plus", "Mission 3-in-1 Cholesterol Test", "EverlyWell"]
                },
                {
                    "name": "Cardiovascular Health Monitor",
                    "description": "Tracks heart health metrics including ECG, heart rate variability, and blood pressure.",
                    "connectivity": "Bluetooth/App",
                    "price_range": "$150-$300",
                    "recommended_brands": ["Withings ScanWatch", "AliveCor KardiaMobile 6L", "Apple Watch Series"]
                }
            ],
            "lung_capacity": [
                {
                    "name": "Home Spirometer",
                    "description": "Measures lung function including FEV1, FVC, and peak flow with trend analysis.",
                    "connectivity": "Bluetooth/App",
                    "price_range": "$100-$300",
                    "recommended_brands": ["Air Smart Spirometer", "NuvoAir", "Spirohome"]
                },
                {
                    "name": "Smart Peak Flow Meter",
                    "description": "Tracks peak expiratory flow rate (PEFR) to monitor respiratory health.",
                    "connectivity": "Bluetooth/App",
                    "price_range": "$50-$150",
                    "recommended_brands": ["Smart Peak Flow", "Propeller Health", "AsthmaMD"]
                },
                {
                    "name": "Air Quality Monitor",
                    "description": "Measures indoor air quality factors that can affect respiratory health.",
                    "connectivity": "WiFi/App",
                    "price_range": "$80-$200",
                    "recommended_brands": ["Awair Element", "uHoo", "AirThings Wave Plus"]
                }
            ],
            "test_results": [
                {
                    "name": "Health Data Hub",
                    "description": "Central device that collects and analyzes data from multiple health devices and tests.",
                    "connectivity": "WiFi/Bluetooth/App",
                    "price_range": "$200-$500",
                    "recommended_brands": ["Apple Health (with iPhone)", "Samsung Health (with Galaxy devices)", "Google Fit (with Android devices)"]
                },
                {
                    "name": "Smart Health Journal",
                    "description": "Digital system for tracking test results, symptoms, and health metrics over time.",
                    "connectivity": "App-based",
                    "price_range": "$0-$50/year",
                    "recommended_brands": ["CareClinic", "Symple", "MyTherapy"]
                }
            ],
            "realtime_health_score": [
                {
                    "name": "Health Tracking Smartwatch",
                    "description": "Comprehensive wearable that tracks multiple health metrics in real-time.",
                    "connectivity": "Bluetooth/WiFi/App",
                    "price_range": "$200-$500",
                    "recommended_brands": ["Apple Watch", "Garmin Venu", "Fitbit Sense", "Samsung Galaxy Watch"]
                },
                {
                    "name": "Smart Scale with Body Composition",
                    "description": "Measures weight, BMI, body fat, muscle mass, and other metrics that contribute to overall health.",
                    "connectivity": "Bluetooth/WiFi/App",
                    "price_range": "$50-$150",
                    "recommended_brands": ["Withings Body+", "Eufy Smart Scale P1", "Garmin Index S2"]
                }
            ]
        }

        # Process health data to identify missing and abnormal vitals
        for vital, mapping in self.vital_device_mapping.items():
            # Check for missing vitals
            if vital not in health_data or health_data[vital] is None or health_data[vital] == 0:
                missing_vitals.append(vital)
                # Add single-purpose device recommendations for missing vitals
                for device in mapping["devices"]:
                    if not any(d["name"] == device["name"] for d in recommended_devices):
                        recommended_devices.append(device)

            # Check for abnormal vitals
            elif vital in health_data and health_data[vital] not in [None, "", "null", "Unknown", 0]:
                # Special handling for blood pressure which has systolic and diastolic components
                if vital == "Blood Pressure":
                    systolic = health_data.get("Blood Pressure (Systolic)")
                    diastolic = health_data.get("Blood Pressure (Diastolic)")

                    if (systolic and (systolic < mapping["abnormal_threshold"]["systolic"]["low"] or
                                     systolic > mapping["abnormal_threshold"]["systolic"]["high"])) or \
                       (diastolic and (diastolic < mapping["abnormal_threshold"]["diastolic"]["low"] or
                                      diastolic > mapping["abnormal_threshold"]["diastolic"]["high"])):
                        abnormal_vitals.append(vital)
                        # Add device recommendations for abnormal blood pressure
                        for device in mapping["devices"]:
                            if not any(d["name"] == device["name"] for d in recommended_devices):
                                recommended_devices.append(device)

                # Handle other vitals
                elif isinstance(health_data[vital], (int, float)):
                    thresholds = mapping["abnormal_threshold"]
                    if health_data[vital] < thresholds["low"] or health_data[vital] > thresholds["high"]:
                        abnormal_vitals.append(vital)
                        # Add device recommendations for abnormal vitals
                        for device in mapping["devices"]:
                            if not any(d["name"] == device["name"] for d in recommended_devices):
                                recommended_devices.append(device)

        # Consider multi-vital devices if multiple vitals are missing or abnormal
        combined_vitals = set(missing_vitals + abnormal_vitals)
        if len(combined_vitals) >= 2:
            for device in self.multi_vital_devices:
                # Check if the multi-vital device covers at least 2 of the missing/abnormal vitals
                covered_vitals = [v for v in device["tracked_vitals"] if v in combined_vitals]
                if len(covered_vitals) >= 2:
                    device_copy = device.copy()
                    device_copy["covered_vitals"] = covered_vitals
                    multi_device_recommendations.append(device_copy)

        # Add test-specific device recommendations if test_type is provided
        if test_type and test_type in test_specific_device_mapping:
            test_specific_devices = test_specific_device_mapping[test_type]

        # Prepare the response
        response = {
            "missing_vitals": missing_vitals,
            "abnormal_vitals": abnormal_vitals,
            "single_device_recommendations": recommended_devices,
            "multi_device_recommendations": multi_device_recommendations,
            "test_specific_devices": test_specific_devices,
            "recommendation_summary": self._generate_recommendation_summary(
                missing_vitals, abnormal_vitals, recommended_devices, multi_device_recommendations,
                test_specific_devices, test_type
            )
        }

        # Add test_type to the response if provided
        if test_type:
            response["test_type"] = test_type

        return response

    def _generate_recommendation_summary(
        self,
        missing_vitals: List[str],
        abnormal_vitals: List[str],
        single_devices: List[Dict],
        multi_devices: List[Dict],
        test_specific_devices: List[Dict] = None,
        test_type: str = None
    ) -> str:
        """Generate a human-readable summary of device recommendations"""
        summary = []

        # Add test-specific introduction if test_type is provided
        if test_type:
            test_type_name = {
                "vital_signs": "vital signs",
                "kidney_function": "kidney function",
                "lipid_profile": "cardiovascular health",
                "lung_capacity": "respiratory health",
                "test_results": "test results",
                "realtime_health_score": "overall health"
            }.get(test_type, "health")

            if missing_vitals and abnormal_vitals:
                summary.append(f"Based on your {test_type_name} data, I've identified some missing measurements and some abnormal readings that could benefit from regular monitoring.")
            elif missing_vitals:
                summary.append(f"I've noticed some important measurements are missing from your {test_type_name} data. Regular monitoring of these could help you track your {test_type_name} more effectively.")
            elif abnormal_vitals:
                summary.append(f"Some of your {test_type_name} measurements are outside the normal range. Regular monitoring with connected devices could help you track these more effectively.")
            else:
                summary.append(f"Your {test_type_name} data looks complete and within normal ranges. However, you might still benefit from connected health devices for ongoing monitoring of your {test_type_name}.")
        else:
            # Generic introduction if no test_type is provided
            if missing_vitals and abnormal_vitals:
                summary.append("Based on your health data, I've identified some missing vital measurements and some abnormal readings that could benefit from regular monitoring.")
            elif missing_vitals:
                summary.append("I've noticed some vital measurements are missing from your health data. Regular monitoring of these could help you track your health more effectively.")
            elif abnormal_vitals:
                summary.append("Some of your vital measurements are outside the normal range. Regular monitoring with connected devices could help you track these more effectively.")
            else:
                summary.append("Your health data looks complete and within normal ranges. However, you might still benefit from connected health devices for ongoing monitoring.")

        # Add missing vitals section
        if missing_vitals:
            summary.append("\nMissing vital measurements:")
            for vital in missing_vitals:
                summary.append(f"• {vital}")

        # Add abnormal vitals section
        if abnormal_vitals:
            summary.append("\nVital measurements outside normal range:")
            for vital in abnormal_vitals:
                summary.append(f"• {vital}")

        # Add test-specific device recommendations if available
        if test_specific_devices:
            test_type_name = {
                "vital_signs": "Vital Signs",
                "kidney_function": "Kidney Function",
                "lipid_profile": "Cardiovascular Health",
                "lung_capacity": "Respiratory Health",
                "test_results": "Test Results",
                "realtime_health_score": "Health Score"
            }.get(test_type, "Health")

            summary.append(f"\nRecommended devices specifically for {test_type_name} monitoring:")
            for device in test_specific_devices[:2]:  # Limit to top 2 test-specific devices
                summary.append(f"• {device['name']} - {device['description']}")
                summary.append(f"  Price range: {device['price_range']}")
                summary.append(f"  Recommended brands: {', '.join(device['recommended_brands'][:2])}")

        # Add multi-function device recommendations
        if multi_devices:
            summary.append("\nRecommended multi-function devices (these can track multiple vitals at once):")
            for device in multi_devices[:2]:  # Limit to top 2 multi-function devices
                covered = ", ".join(device["covered_vitals"])
                summary.append(f"• {device['name']} - Tracks: {covered} - Price range: {device['price_range']}")
                summary.append(f"  Recommended brands: {', '.join(device['recommended_brands'][:2])}")

        # Add single-function device recommendations
        if single_devices:
            summary.append("\nRecommended single-function devices:")
            # Group devices by vital
            vital_to_devices = {}
            for device in single_devices:
                for vital, mapping in self.vital_device_mapping.items():
                    if device in mapping["devices"]:
                        if vital not in vital_to_devices:
                            vital_to_devices[vital] = []
                        vital_to_devices[vital].append(device)

            # Add top device for each vital
            for vital, devices in vital_to_devices.items():
                if devices:
                    device = devices[0]  # Take the first device for each vital
                    summary.append(f"• For {vital}: {device['name']} - {device['price_range']}")
                    summary.append(f"  Recommended brands: {', '.join(device['recommended_brands'][:2])}")

        # Add conclusion based on test type
        if test_type:
            test_type_name = {
                "vital_signs": "vital signs",
                "kidney_function": "kidney function",
                "lipid_profile": "cardiovascular health",
                "lung_capacity": "respiratory health",
                "test_results": "test results",
                "realtime_health_score": "overall health"
            }.get(test_type, "health")

            summary.append(f"\nRegular monitoring of your {test_type_name} can help you maintain better health and detect potential issues early. Consider discussing these device options with your healthcare provider.")
        else:
            summary.append("\nRegular monitoring of your health metrics can help you maintain better health and detect potential issues early. Consider discussing these device options with your healthcare provider.")

        # Add device recommendation confirmation prompt
        summary.append("\n🛒 Would you like to explore these health devices? If yes, I can direct you to our recommended health device store where you can find these products and more.")

        return "\n".join(summary)

    def generate_device_confirmation_response(self, user_confirmed: bool = True) -> Dict:
        """
        Generate response for device recommendation confirmation.

        Args:
            user_confirmed: Whether user confirmed they want device recommendations

        Returns:
            Dictionary with confirmation response and redirect information
        """
        if user_confirmed:
            return {
                "confirmation": True,
                "message": "Great! I'm redirecting you to our recommended health device store where you can find all the devices I mentioned and many more health monitoring solutions.",
                "redirect_url": "https://www.turbomedics.com/products",
                "redirect_message": "🛒 **[Click here to visit TurboMedics Health Store](https://www.turbomedics.com/products)**\n\nYou'll find a wide selection of:\n• Connected health devices\n• Medical monitoring equipment\n• Health tracking solutions\n• Professional-grade health tools\n\nVisit our store to browse our recommended health devices!",
                "additional_info": {
                    "store_features": [
                        "Professional-grade health monitoring devices",
                        "Connected health solutions with app integration",
                        "Expert recommendations and reviews",
                        "Competitive pricing and warranty options",
                        "Customer support for device selection"
                    ],
                    "categories": [
                        "Blood Pressure Monitors",
                        "Glucose Monitoring Systems",
                        "Heart Rate & ECG Devices",
                        "Respiratory Health Tools",
                        "Smart Scales & Body Composition",
                        "Multi-function Health Stations"
                    ]
                }
            }
        else:
            return {
                "confirmation": False,
                "message": "No problem! If you change your mind later, just let me know and I can provide device recommendations or direct you to our health device store.",
                "alternative_suggestions": [
                    "Continue monitoring your health with regular check-ups",
                    "Keep track of your health metrics manually",
                    "Discuss device options with your healthcare provider",
                    "Ask me again anytime if you'd like device recommendations"
                ]
            }

# Function to be called by the tool
def recommend_health_devices(health_data_json: str) -> str:
    """
    Recommend connected health devices based on missing or abnormal vitals.

    Args:
        health_data_json: JSON string containing health data and optional test_type

    Returns:
        JSON string with device recommendations
    """
    try:
        # Parse the input JSON
        input_data = json.loads(health_data_json)

        # Extract the data field and test_type if they exist
        health_data = input_data.get("data", input_data)
        test_type = input_data.get("test_type")

        # Create recommender and get recommendations
        recommender = DeviceRecommender()
        recommendations = recommender.recommend_devices(health_data, test_type)

        # Return the recommendations as a JSON string
        return json.dumps(recommendations, indent=2)

    except Exception as e:
        error_response = {
            "error": f"Error processing device recommendations: {str(e)}",
            "recommendation_summary": "Unable to provide device recommendations due to an error in processing your health data."
        }

        # Add test_type to the error response if it exists in the input
        try:
            input_data = json.loads(health_data_json)
            if "test_type" in input_data:
                error_response["test_type"] = input_data["test_type"]
        except:
            pass

        return json.dumps(error_response, indent=2)

def confirm_device_recommendation(confirmation_json: str) -> str:
    """
    Handle user confirmation for device recommendations and provide redirect information.

    Args:
        confirmation_json: JSON string containing user confirmation ({"confirmed": true/false})

    Returns:
        JSON string with confirmation response and redirect information
    """
    try:
        # Parse the input JSON
        input_data = json.loads(confirmation_json)
        user_confirmed = input_data.get("confirmed", True)

        # Create recommender and get confirmation response
        recommender = DeviceRecommender()
        response = recommender.generate_device_confirmation_response(user_confirmed)

        # Return the response as a JSON string
        return json.dumps(response, indent=2)

    except Exception as e:
        error_response = {
            "error": f"Error processing device recommendation confirmation: {str(e)}",
            "message": "Unable to process your confirmation. Please try again or contact support."
        }
        return json.dumps(error_response, indent=2)

# Create Tool instances for use with LangChain
device_recommender_tool = Tool(
    name="DeviceRecommender",
    func=recommend_health_devices,
    description="Suggests connected health devices based on missing or abnormal vitals in health data. Includes a confirmation prompt for device recommendations."
)

device_confirmation_tool = Tool(
    name="DeviceConfirmation",
    func=confirm_device_recommendation,
    description="Handles user confirmation for device recommendations and provides redirect information to www.turbomedics.com/products when user confirms they want to explore devices."
)
