def enrich_kidney_parameters(data: dict) -> dict:
    if not data.get("BUN") and data.get("Serum Urea"):
        data["BUN"] = data["Serum Urea"] / 2.14
    if not data.get("ACR") and data.get("Urine Albumin") and data.get("Urine Creatinine"):
        data["ACR"] = data["Urine Albumin"] / data["Urine Creatinine"]
    if not data.get("BUN/Creatinine Ratio") and data.get("BUN") and data.get("Serum Creatinine"):
        data["BUN/Creatinine Ratio"] = data["BUN"] / data["Serum Creatinine"]
    if not data.get("Urea/Creatinine Ratio") and data.get("Serum Urea") and data.get("Serum Creatinine"):
        data["Urea/Creatinine Ratio"] = data["Serum Urea"] / data["Serum Creatinine"]
    if not data.get("eGFR") and data.get("Serum Creatinine") and data.get("Age") and data.get("Sex"):
        k = 0.742 if data["Sex"].lower() == "female" else 1.0
        data["eGFR"] = 186 * (data["Serum Creatinine"] ** -1.154) * (data["Age"] ** -0.203) * k
    return data

def analyze_kidney_function(data: dict):
    analysis = []
    missing_parameters = []
    parameter_info = {
    "BUN": {"range": (7, 20), "elevated": "High levels may indicate kidney dysfunction or dehydration.", "decreased": "Low levels may indicate malnutrition or liver disease."},
    "Serum Urea": {"range": (2.5, 7.1), "elevated": "Elevated levels may suggest kidney issues or high protein intake.", "decreased": "Low levels may indicate malnutrition or liver disease."},
    "Serum Creatinine": {"range": (0.6, 1.2), "elevated": "High levels may indicate impaired kidney function.", "decreased": "Low levels may indicate reduced muscle mass."},
    "eGFR": {"range": (90, float("inf")), "elevated": None, "decreased": "Lower levels indicate reduced kidney filtration capacity."},
    "BUN/Creatinine Ratio": {"range": (10, 20), "elevated": "Elevated BUN/Creatinine Ratio may indicate dehydration or reduced kidney perfusion.", "decreased": "Low BUN/Creatinine Ratio may indicate liver disease or malnutrition."},
    "Urea/Creatinine Ratio": {"range": (40, 100), "elevated": "High Urea/Creatinine Ratio may indicate dehydration or high protein intake.", "decreased": "Low Urea/Creatinine Ratio may indicate liver disease or malnutrition."},
    "Serum Sodium": {"range": (135, 145), "elevated": "High levels may indicate dehydration.", "decreased": "Low levels may indicate overhydration or kidney dysfunction."},
    "Serum Potassium": {"range": (3.5, 5.0), "elevated": "High levels may indicate kidney dysfunction or acidosis.", "decreased": "Low levels may indicate alkalosis or diuretic use."},
    "Serum Calcium": {"range": (8.8, 10.2), "elevated": "High levels may indicate hyperparathyroidism or cancer.", "decreased": "Low levels may indicate kidney disease or vitamin D deficiency."},
    "Serum Uric Acid": {"range": (3.5, 7.2), "elevated": "High levels may indicate gout or kidney dysfunction.", "decreased": "Low levels may indicate liver disease."},
    "Chloride": {"range": (96, 106), "elevated": "High levels may indicate dehydration.", "decreased": "Low levels may indicate alkalosis."},
    "Bicarbonate": {"range": (22, 29), "elevated": "High levels may indicate metabolic alkalosis.", "decreased": "Low levels may indicate metabolic acidosis."},
    "ACR": {"range": (None, 30), "elevated": "High levels indicate increased albumin excretion, a marker of kidney damage.", "decreased": None}
}

    # Analyze each parameter
    for param, value in data.items():
        if value is None or value == "Unknown":
            missing_parameters.append(param)
        elif param in parameter_info:
            info = parameter_info[param]
            low, high = info["range"]
            if low is not None and high is not None:
                if low <= value <= high:
                    analysis.append(f"{param}: {value} → Normal")
                elif value > high:
                    analysis.append(f"{param}: {value} → High (Above Normal Range). {info['elevated']}")
                elif value < low:
                    analysis.append(f"{param}: {value} → Low (Below Normal Range). {info['decreased']}")
            elif high is not None and value > high:
                analysis.append(f"{param}: {value} → High (Above Normal Range). {info['elevated']}")
            elif low is not None and value < low:
                analysis.append(f"{param}: {value} → Low (Below Normal Range). {info['decreased']}")

    # Add detailed analysis for key parameters
    if "BUN" in data and data["BUN"] is not None:
        bun = data["BUN"]
        if bun > 20:
            severity = "mildly" if bun <= 30 else "moderately" if bun <= 50 else "severely"
            analysis.append(f"BUN Detail: Your BUN level of {bun} is {severity} elevated, which may indicate reduced kidney function, dehydration, or increased protein breakdown.")
        elif bun < 7:
            analysis.append(f"BUN Detail: Your BUN level of {bun} is below normal, which may indicate malnutrition, liver disease, or overhydration.")
        else:
            analysis.append(f"BUN Detail: Your BUN level of {bun} is within normal range, suggesting normal kidney function in terms of urea clearance.")

    if "Serum Creatinine" in data and data["Serum Creatinine"] is not None:
        creatinine = data["Serum Creatinine"]
        if creatinine > 1.2:
            severity = "mildly" if creatinine <= 2.0 else "moderately" if creatinine <= 5.0 else "severely"
            analysis.append(f"Creatinine Detail: Your serum creatinine of {creatinine} is {severity} elevated, indicating reduced kidney filtration capacity. This is a significant marker of kidney function.")
        elif creatinine < 0.6:
            analysis.append(f"Creatinine Detail: Your serum creatinine of {creatinine} is below normal, which may indicate reduced muscle mass or malnutrition.")
        else:
            analysis.append(f"Creatinine Detail: Your serum creatinine of {creatinine} is within normal range, suggesting adequate kidney filtration capacity.")

    # Add eGFR stages with detailed explanation
    if "eGFR" in data and data["eGFR"] is not None:
        egfr = data["eGFR"]
        if egfr >= 90:
            stage = "G1 - Normal or High"
            explanation = "Your kidney function appears normal. This suggests your kidneys are filtering blood efficiently."
            ckd_status = "With normal eGFR, you may not have chronic kidney disease unless other markers of kidney damage are present."
        elif 60 <= egfr < 90:
            stage = "G2 - Mildly Decreased"
            explanation = "Your kidney function is mildly reduced. This may be a normal aging-related decline or an early sign of kidney disease."
            ckd_status = "This stage may represent early chronic kidney disease, especially if other markers of kidney damage are present."
        elif 45 <= egfr < 60:
            stage = "G3a - Mild to Moderate Decrease"
            explanation = "Your kidney function shows a mild to moderate reduction. This indicates some loss of kidney filtering capacity."
            ckd_status = "This represents Stage 3a chronic kidney disease, which requires regular monitoring."
        elif 30 <= egfr < 45:
            stage = "G3b - Moderate to Severe Decrease"
            explanation = "Your kidney function shows a moderate to severe reduction. This indicates significant loss of kidney filtering capacity."
            ckd_status = "This represents Stage 3b chronic kidney disease, which requires regular monitoring and may need specialist care."
        elif 15 <= egfr < 30:
            stage = "G4 - Severely Decreased"
            explanation = "Your kidney function is severely reduced. This indicates advanced kidney disease."
            ckd_status = "This represents Stage 4 chronic kidney disease, which requires nephrology care and preparation for possible kidney replacement therapy."
        else:
            stage = "G5 - Kidney Failure"
            explanation = "Your kidney function indicates kidney failure. This is the most severe stage of kidney disease."
            ckd_status = "This represents Stage 5 chronic kidney disease (kidney failure), which typically requires kidney replacement therapy (dialysis or transplant)."

        analysis.append(f"eGFR Detail: {egfr} mL/min/1.73m² → {stage}")
        analysis.append(f"eGFR Explanation: {explanation}")
        analysis.append(f"CKD Status: {ckd_status}")

    # Add ACR categories if available
    if "ACR" in data and data["ACR"] is not None:
        acr = data["ACR"]
        if acr < 30:
            category = "A1 - Normal to Mildly Increased"
            albuminuria_status = "Your albumin-to-creatinine ratio is normal or only mildly elevated, suggesting minimal or no kidney damage."
        elif 30 <= acr < 300:
            category = "A2 - Moderately Increased"
            albuminuria_status = "Your albumin-to-creatinine ratio shows moderate albuminuria, indicating some kidney damage."
        else:
            category = "A3 - Severely Increased"
            albuminuria_status = "Your albumin-to-creatinine ratio shows severe albuminuria, indicating significant kidney damage."

        analysis.append(f"ACR Detail: {acr} mg/g → {category}")
        analysis.append(f"Albuminuria Status: {albuminuria_status}")

    # Calculate ACR if not provided but Urine Albumin and Urine Creatinine are available
    elif "Urine Albumin" in data and data["Urine Albumin"] is not None and "Urine Creatinine" in data and data["Urine Creatinine"] is not None:
        urine_albumin = data["Urine Albumin"]
        urine_creatinine = data["Urine Creatinine"]
        if urine_creatinine > 0:  # Avoid division by zero
            acr = (urine_albumin / urine_creatinine) * 1000  # Convert to mg/g
            if acr < 30:
                category = "A1 - Normal to Mildly Increased"
                albuminuria_status = "Your calculated albumin-to-creatinine ratio is normal or only mildly elevated, suggesting minimal or no kidney damage."
            elif 30 <= acr < 300:
                category = "A2 - Moderately Increased"
                albuminuria_status = "Your calculated albumin-to-creatinine ratio shows moderate albuminuria, indicating some kidney damage."
            else:
                category = "A3 - Severely Increased"
                albuminuria_status = "Your calculated albumin-to-creatinine ratio shows severe albuminuria, indicating significant kidney damage."

            analysis.append(f"Calculated ACR: {acr:.1f} mg/g → {category}")
            analysis.append(f"Albuminuria Status: {albuminuria_status}")

    return analysis, missing_parameters

def reorder_extracted_data(data):
    """
    Reorder the extracted data dictionary to ensure calculated values (ACR, eGFR, BUN/Creatinine Ratio, Urea/Creatinine Ratio, BUN)
    are listed last.
    """
    calculated_keys = ["ACR", "eGFR", "BUN/Creatinine Ratio", "Urea/Creatinine Ratio", "BUN"]
    reordered_data = {key: value for key, value in data.items() if key not in calculated_keys}
    for key in calculated_keys:
        if key in data:
            reordered_data[key] = data[key]
    return reordered_data

def kidney_function_analysis_tool(input_data: dict) -> dict:
    enriched_data = enrich_kidney_parameters(input_data)
    enriched_data = reorder_extracted_data(enriched_data)
    analysis, missing = analyze_kidney_function(enriched_data)

    # Initialize recommendation string
    overall_health = "Unable to determine overall kidney health due to missing data."

    # Create a doctor-like summary
    doctor_summary = "Hello! I've reviewed your kidney function test results, and here's my assessment: "

    # Retrieve key parameters
    egfr = enriched_data.get("eGFR")
    acr = enriched_data.get("ACR")
    bun = enriched_data.get("BUN")
    creatinine = enriched_data.get("Serum Creatinine")
    bun_creatinine_ratio = enriched_data.get("BUN/Creatinine Ratio")
    sodium = enriched_data.get("Serum Sodium")
    potassium = enriched_data.get("Serum Potassium")
    bicarbonate = enriched_data.get("Bicarbonate")
    chloride = enriched_data.get("Chloride")
    uric_acid = enriched_data.get("Serum Uric Acid")

    # Initialize kidney disease stage and renal health management recommendations
    kidney_stage = "Unknown"
    recommendations = []

    # Apply comprehensive logic for interpreting overall kidney status
    if egfr is not None and acr is not None:
        if egfr >= 90 and acr < 30:
            overall_health = "✅ Your kidney health is normal."
            doctor_summary += "Good news! Your kidney function appears to be in the healthy range. "
            kidney_stage = "G1 (Normal kidney function)"
            recommendations.append("Maintain a healthy lifestyle with regular exercise and balanced diet.")
            recommendations.append("Stay well-hydrated by drinking 6-8 glasses of water daily.")
            recommendations.append("Continue routine kidney function monitoring annually.")
        elif egfr < 90 or acr >= 30:
            if egfr < 15:
                overall_health = "❗ You may have kidney failure (Stage G5). Immediate medical attention is required."
                doctor_summary += "I'm seeing critical concerns with your kidney function that require immediate medical attention. "
                kidney_stage = "G5 (Kidney failure)"
                recommendations.append("Seek immediate nephrology consultation for dialysis or transplant evaluation.")
                recommendations.append("Follow a strict renal diet with potassium, phosphorus, and sodium restrictions.")
                recommendations.append("Monitor fluid intake carefully according to your nephrologist's guidance.")
                recommendations.append("Take medications exactly as prescribed to manage complications.")
            elif egfr < 30:
                overall_health = "❗ You may have severe kidney disease (Stage G4). Immediate medical attention is recommended."
                doctor_summary += "I'm seeing significant concerns with your kidney function that require prompt attention. "
                kidney_stage = "G4 (Severe CKD)"
                recommendations.append("Schedule an appointment with a nephrologist within 2 weeks.")
                recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
                recommendations.append("Reduce protein intake to 0.6-0.8g/kg body weight daily.")
                recommendations.append("Avoid NSAIDs and other nephrotoxic medications.")
            elif egfr < 45:
                overall_health = "⚠️ You may have moderate to severe kidney impairment (Stage G3b). Consult a nephrologist."
                doctor_summary += "I'm noticing concerning patterns in your kidney function that require specialist attention. "
                kidney_stage = "G3b (Moderate to Severe CKD)"
                recommendations.append("Consult with a nephrologist within 1 month.")
                recommendations.append("Monitor blood pressure closely, aiming for <130/80 mmHg.")
                recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
                recommendations.append("Consider reducing protein intake to 0.8g/kg body weight daily.")
            elif egfr < 60:
                overall_health = "⚠️ You may have moderate kidney impairment (Stage G3a). Consult a nephrologist."
                doctor_summary += "I'm noticing some concerning patterns in your kidney function that we should address. "
                kidney_stage = "G3a (Moderate CKD)"
                recommendations.append("Schedule a nephrology consultation within 3 months.")
                recommendations.append("Monitor blood pressure regularly, aiming for <130/80 mmHg.")
                recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
                recommendations.append("Stay well-hydrated but avoid excessive fluid intake.")
            else:
                overall_health = "ℹ️ You may have mild kidney impairment (Stage G2). Routine monitoring and lifestyle changes are recommended."
                doctor_summary += "I'm seeing some mild changes in your kidney function that we should monitor. "
                kidney_stage = "G2 (Mild CKD)"
                recommendations.append("Follow up with your primary care physician in 6 months.")
                recommendations.append("Maintain blood pressure below 130/80 mmHg.")
                recommendations.append("Adopt a heart-healthy diet with moderate sodium restriction.")
                recommendations.append("Exercise regularly for at least 150 minutes per week.")
    elif egfr is not None:
        if egfr >= 90:
            overall_health = "✅ Your kidney filtration rate is normal (Stage G1)."
            doctor_summary += "Good news! Your kidney filtration rate is in the normal range. "
            kidney_stage = "G1 (Normal kidney function)"
            recommendations.append("Maintain a healthy lifestyle with regular exercise and balanced diet.")
            recommendations.append("Stay well-hydrated by drinking 6-8 glasses of water daily.")
            recommendations.append("Continue routine kidney function monitoring annually.")
        elif egfr < 15:
            overall_health = "❗ You may have kidney failure (Stage G5). Immediate medical attention is required."
            doctor_summary += "I'm seeing critical concerns with your kidney function that require immediate medical attention. "
            kidney_stage = "G5 (Kidney failure)"
            recommendations.append("Seek immediate nephrology consultation for dialysis or transplant evaluation.")
            recommendations.append("Follow a strict renal diet with potassium, phosphorus, and sodium restrictions.")
            recommendations.append("Monitor fluid intake carefully according to your nephrologist's guidance.")
            recommendations.append("Take medications exactly as prescribed to manage complications.")
        elif egfr < 30:
            overall_health = "❗ You may have severe kidney disease (Stage G4). Immediate medical attention is recommended."
            doctor_summary += "I'm seeing significant concerns with your kidney function that require prompt attention. "
            kidney_stage = "G4 (Severe CKD)"
            recommendations.append("Schedule an appointment with a nephrologist within 2 weeks.")
            recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
            recommendations.append("Reduce protein intake to 0.6-0.8g/kg body weight daily.")
            recommendations.append("Avoid NSAIDs and other nephrotoxic medications.")
        elif egfr < 45:
            overall_health = "⚠️ You may have moderate to severe kidney impairment (Stage G3b). Consult a nephrologist."
            doctor_summary += "I'm noticing concerning patterns in your kidney function that require specialist attention. "
            kidney_stage = "G3b (Moderate to Severe CKD)"
            recommendations.append("Consult with a nephrologist within 1 month.")
            recommendations.append("Monitor blood pressure closely, aiming for <130/80 mmHg.")
            recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
            recommendations.append("Consider reducing protein intake to 0.8g/kg body weight daily.")
        elif egfr < 60:
            overall_health = "⚠️ You may have moderate kidney impairment (Stage G3a). Consult a nephrologist."
            doctor_summary += "I'm noticing some concerning patterns in your kidney function that we should address. "
            kidney_stage = "G3a (Moderate CKD)"
            recommendations.append("Schedule a nephrology consultation within 3 months.")
            recommendations.append("Monitor blood pressure regularly, aiming for <130/80 mmHg.")
            recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
            recommendations.append("Stay well-hydrated but avoid excessive fluid intake.")
        else:
            overall_health = "ℹ️ You may have mild kidney impairment (Stage G2). Routine monitoring and lifestyle changes are recommended."
            doctor_summary += "I'm seeing some mild changes in your kidney function that we should monitor. "
            kidney_stage = "G2 (Mild CKD)"
            recommendations.append("Follow up with your primary care physician in 6 months.")
            recommendations.append("Maintain blood pressure below 130/80 mmHg.")
            recommendations.append("Adopt a heart-healthy diet with moderate sodium restriction.")
            recommendations.append("Exercise regularly for at least 150 minutes per week.")
    elif acr is not None:
        if acr < 30:
            overall_health = "✅ Your albumin-to-creatinine ratio is normal."
            doctor_summary += "Good news! Your albumin-to-creatinine ratio is in the normal range. "
            recommendations.append("Continue annual kidney function screening.")
            recommendations.append("Maintain a healthy lifestyle with regular exercise and balanced diet.")
        else:
            overall_health = "⚠️ You may have kidney damage. Consult a nephrologist for further evaluation."
            doctor_summary += "I'm noticing some concerning patterns in your kidney function that we should address. "
            recommendations.append("Schedule a nephrology consultation within 1-3 months.")
            recommendations.append("Monitor blood pressure closely, aiming for <130/80 mmHg.")
            recommendations.append("Limit dietary sodium to less than 2,300mg daily.")
            if acr >= 300:
                recommendations.append("You have significant albuminuria which requires close monitoring and treatment.")
    else:
        overall_health = "❓ Insufficient data to fully assess kidney health. Please provide eGFR or ACR."
        doctor_summary += "I don't have enough information to give you a complete assessment of your kidney health. "
        recommendations.append("Complete a comprehensive kidney function panel including eGFR and ACR.")

    # Add BUN and Creatinine specific recommendations
    if bun is not None:
        if bun > 20:
            if bun > 50:
                recommendations.append("Your BUN is severely elevated, which may indicate significant kidney dysfunction or dehydration.")
                recommendations.append("Limit protein intake and ensure adequate hydration unless otherwise directed by your doctor.")
            elif bun > 30:
                recommendations.append("Your BUN is moderately elevated. Consider reducing protein intake and ensure adequate hydration.")
            else:
                recommendations.append("Your BUN is mildly elevated. Ensure adequate hydration and monitor your protein intake.")
        elif bun < 7:
            recommendations.append("Your BUN is below normal range, which may indicate overhydration or liver issues.")
            recommendations.append("Discuss with your healthcare provider about appropriate fluid intake and possible liver function testing.")

    if creatinine is not None:
        if creatinine > 1.2:
            if creatinine > 5.0:
                recommendations.append("Your serum creatinine is severely elevated, indicating significant kidney dysfunction.")
                recommendations.append("Avoid nephrotoxic medications and follow a renal-protective diet as directed by your nephrologist.")
            elif creatinine > 2.0:
                recommendations.append("Your serum creatinine is moderately elevated. Avoid nephrotoxic medications like NSAIDs.")
                recommendations.append("Follow up with a nephrologist to develop a kidney protection plan.")
            else:
                recommendations.append("Your serum creatinine is mildly elevated. Avoid nephrotoxic medications and stay well-hydrated.")
        elif creatinine < 0.6:
            recommendations.append("Your serum creatinine is below normal range, which may indicate decreased muscle mass.")
            recommendations.append("Consider discussing protein intake and exercise with your healthcare provider.")

    # Add recommendations based on electrolyte abnormalities
    if sodium is not None:
        if sodium > 145:
            recommendations.append("Your serum sodium is elevated. Reduce salt intake and increase water consumption.")
        elif sodium < 135:
            recommendations.append("Your serum sodium is low. Consult with your doctor about proper fluid and electrolyte management.")

    if potassium is not None:
        if potassium > 5.0:
            recommendations.append("Your serum potassium is elevated. Limit high-potassium foods like bananas, oranges, and potatoes.")
            if potassium > 6.0:
                recommendations.append("Your potassium level is significantly elevated, which can affect heart function. Seek medical attention.")
        elif potassium < 3.5:
            recommendations.append("Your serum potassium is low. Discuss with your doctor about possible potassium supplementation.")

    # Track any still-missing fields for confidence and recommendations
    missing_parameters = [param for param in [
        "eGFR", "ACR", "Serum Creatinine", "Serum Urea", "Serum Potassium",
        "Bicarbonate", "Chloride", "Serum Calcium", "Serum Uric Acid",
        "BUN", "BUN/Creatinine Ratio", "Urea/Creatinine Ratio"
    ] if enriched_data.get(param) is None]

    # Add a note about confidence to the doctor summary
    if not missing_parameters:
        doctor_summary += "Your test provided comprehensive data, so I'm quite confident in this assessment."
    elif len(missing_parameters) <= 3:
        doctor_summary += "Your test provided most of the key measurements, giving us a reasonably good picture of your kidney health."
    else:
        doctor_summary += f"We're missing some measurements ({', '.join(missing_parameters)}), so this is a preliminary assessment."

    if missing_parameters:
        overall_health += f"\n🔎 Note: Assessment based on incomplete data. Missing parameters: {', '.join(missing_parameters)}."

    # Determine confidence level
    confidence = "High" if not missing_parameters else "Medium" if len(missing_parameters) <= 3 else "Low"

    # Add general kidney health recommendations if we have some data
    if egfr is not None or acr is not None or bun is not None or creatinine is not None:
        if "Maintain a healthy lifestyle" not in " ".join(recommendations):
            recommendations.append("Maintain a healthy lifestyle with regular exercise and balanced diet.")
        if "Stay well-hydrated" not in " ".join(recommendations):
            recommendations.append("Stay well-hydrated by drinking 6-8 glasses of water daily unless otherwise directed.")
        if "Monitor blood pressure" not in " ".join(recommendations):
            recommendations.append("Monitor blood pressure regularly, aiming for <130/80 mmHg.")

    # Make recommendations unique
    unique_recommendations = []
    for rec in recommendations:
        if rec not in unique_recommendations:
            unique_recommendations.append(rec)

    # Add device recommendation prompt (following agent_app pattern)
    device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your kidney function results? (Yes/No)**"

    return {
        "analysis": analysis,
        "overall_health": overall_health,
        "confidence_level": confidence,
        "missing_parameters": missing_parameters,
        "data": enriched_data,
        "doctor_summary": doctor_summary,
        "kidney_stage": kidney_stage,
        "recommendations": unique_recommendations,
        "device_recommendation_prompt": device_recommendation_prompt,
        "test_type": "kidney_function"
    }


# === Optional CLI Interface for standalone use ===

def get_manual_input():
    extracted_data = {
        "Serum Urea": float(input("Serum Urea (mg/dL): ")),
        "Serum Creatinine": float(input("Serum Creatinine (mg/dL): ")),
        "Serum Sodium": float(input("Serum Sodium (mmol/L): ")),
        "Serum Potassium": float(input("Serum Potassium (mmol/L): ")),
        "Serum Calcium": float(input("Serum Calcium (mg/dL): ")),
        "Serum Uric Acid": float(input("Serum Uric Acid (mg/dL): ")),
        "Urine Albumin": float(input("Urine Albumin (mg/dL): ")),
        "Urine Creatinine": float(input("Urine Creatinine (mg/dL): ")),
        "Chloride": float(input("Chloride (mmol/L): ")),
        "Bicarbonate": float(input("Bicarbonate (mmol/L): ")),
        "Age": int(input("Age (years): ")),
        "Sex": input("Sex (Male/Female): ").strip().capitalize(),
        # Optional parameters
        "BUN": float(input("BUN (optional, leave blank for system to calculate): ") or 0.0),
        "ACR": float(input("ACR (optional, leave blank for system to calculate): ") or 0.0),
        "BUN/Creatinine Ratio": float(input("BUN/Creatinine Ratio (optional, leave blank for system to calculate): ") or 0.0),
        "Urea/Creatinine Ratio": float(input("Urea/Creatinine Ratio (optional, leave blank for system to calculate): ") or 0.0),
        "eGFR": float(input("eGFR (optional, leave blank for system to calculate): ") or 0.0)
    }

    # Calculate BUN if not provided but Serum Urea is available
    if extracted_data["BUN"] == 0.0 and extracted_data["Serum Urea"] != 0.0:
        extracted_data["BUN"] = extracted_data["Serum Urea"] / 2.14

    # Calculate ACR if not provided but Urine Albumin and Urine Creatinine are available
    if extracted_data["ACR"] == 0.0 and extracted_data["Urine Albumin"] != 0.0 and extracted_data["Urine Creatinine"] != 0.0:
        extracted_data["ACR"] = extracted_data["Urine Albumin"] / extracted_data["Urine Creatinine"]

    # Calculate BUN/Creatinine Ratio if not provided but BUN and Serum Creatinine are available
    if extracted_data["BUN/Creatinine Ratio"] == 0.0 and extracted_data["BUN"] != 0.0 and extracted_data["Serum Creatinine"] != 0.0:
        extracted_data["BUN/Creatinine Ratio"] = extracted_data["BUN"] / extracted_data["Serum Creatinine"]

    # Calculate Urea/Creatinine Ratio if not provided but Serum Urea and Serum Creatinine are available
    if extracted_data["Urea/Creatinine Ratio"] == 0.0 and extracted_data["Serum Urea"] != 0.0 and extracted_data["Serum Creatinine"] != 0.0:
        extracted_data["Urea/Creatinine Ratio"] = extracted_data["Serum Urea"] / extracted_data["Serum Creatinine"]

    # Calculate eGFR if not provided but Age, Sex, and Serum Creatinine are available
    if extracted_data["eGFR"] == 0.0 and extracted_data["Age"] != 0 and extracted_data["Serum Creatinine"] != 0.0:
        k = 0.742 if extracted_data["Sex"] == "Female" else 1.0
        extracted_data["eGFR"] = 186 * (extracted_data["Serum Creatinine"] ** -1.154) * (extracted_data["Age"] ** -0.203) * k

    # Reorder calculated values to come last
    extracted_data = reorder_extracted_data(extracted_data)

    return extracted_data

if __name__ == "__main__":
    print("🔍 Kidney Function CLI Tool")
    extracted_data = get_manual_input()

    # Reorder extracted data to ensure calculated values come last
    extracted_data = reorder_extracted_data(extracted_data)

    print("\n🧪 Extracted Data:")
    for key, value in extracted_data.items():
        print(f"{key}: {value if value is not None else 'Not Provided'}")

    # Generate findings and missing parameters
    analysis, missing_parameters = analyze_kidney_function(extracted_data)

    print(f"\n🔬 Kidney Function Analysis:")
    for key in extracted_data.keys():
        for result in analysis:
            if result.startswith(key):
                print(f"   - {result}")

    # Assess kidney health based on available parameters
    print("\n📊 Findings Summary:")
    overall_health = "Unable to determine overall kidney health due to missing data."

    egfr = extracted_data.get("eGFR")
    acr = extracted_data.get("ACR")
    bun_creatinine_ratio = extracted_data.get("BUN/Creatinine Ratio")
    sodium = extracted_data.get("Serum Sodium")
    potassium = extracted_data.get("Serum Potassium")
    bicarbonate = extracted_data.get("Bicarbonate")
    chloride = extracted_data.get("Chloride")
    uric_acid = extracted_data.get("Serum Uric Acid")

    if egfr is not None and acr is not None:
        if egfr >= 90 and acr < 30:
            overall_health = "✅ Your kidney health is normal."
        elif egfr < 90 or acr >= 30:
            if egfr < 30:
                overall_health = "❗ You may have severe kidney disease. Immediate medical attention is recommended."
            elif egfr < 60 or acr >= 300:
                overall_health = "⚠️ You may have moderate kidney impairment. Consult a nephrologist."
            else:
                overall_health = "ℹ️ You may have mild kidney impairment. Routine monitoring and lifestyle changes are recommended."
    elif egfr is not None:
        if egfr >= 90:
            overall_health = "✅ Your kidney filtration rate is normal."
        elif egfr < 30:
            overall_health = "❗ You may have severe kidney disease. Immediate medical attention is recommended."
        elif egfr < 60:
            overall_health = "⚠️ You may have moderate kidney impairment. Consult a nephrologist."
        else:
            overall_health = "ℹ️ You may have mild kidney impairment. Routine monitoring and lifestyle adjustments are recommended."
    elif acr is not None:
        if acr < 30:
            overall_health = "✅ Your albumin-to-creatinine ratio is normal."
        else:
            overall_health = "⚠️ You may have kidney damage. Consult a nephrologist for further evaluation."
    else:
        overall_health = "❓ Insufficient data to fully assess kidney health. Please provide eGFR or ACR."

    # List truly missing parameters for reference
    missing_parameters = [param for param in [
        "eGFR", "ACR", "Serum Creatinine", "Serum Urea", "Serum Potassium",
        "Bicarbonate", "Chloride", "Serum Calcium", "Serum Uric Acid",
        "BUN", "BUN/Creatinine Ratio", "Urea/Creatinine Ratio"
    ] if extracted_data.get(param) is None]

    if missing_parameters:
        overall_health += f"\nNote: This is based on incomplete data. Missing parameters: {', '.join(missing_parameters)}."

    print(overall_health)

    # Add confidence level
    confidence_level = "High" if not missing_parameters else "Medium" if len(missing_parameters) <= 3 else "Low"
    print(f"\n📈 Confidence Level: {confidence_level}")
    if missing_parameters:
        print(f"\n🧪 Additional tests recommended: {', '.join(missing_parameters)}")
