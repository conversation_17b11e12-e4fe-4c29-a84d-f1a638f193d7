# tools/tools_lipid_profile.py

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import re

# Reference ranges
REF_RANGES = {
    'total_chol': {'low': '<90', 'high': '≥240', 'borderline': '≥200-239.9', 'optimal': '90-199.9'},
    'ldl': {'low': '<100', 'high': '≥160', 'borderline': '130-159.9', 'optimal': '100-129.9'},
    'hdl_male': {'low': '<40', 'high': '≥60', 'borderline': '40-45', 'optimal': '46-59.9'},
    'hdl_female': {'low': '<50', 'high': '≥60', 'borderline': '50-55', 'optimal': '56-59.9'},
    'triglycerides': {'low': '<60', 'high': '≥200', 'borderline': '150-199.9', 'optimal': '60-149.9'},
    'non_hdl': {'low': '<70', 'high': '≥160', 'borderline': '130-159.9', 'optimal': '70-129.9'},
    'vldl': {'low': '<10', 'high': '≥40', 'borderline': '31-39.9', 'optimal': '10-30'},
}

def classify_component(component, value, sex=None):
    """Classify each lipid component"""
    # Handle Unknown values
    if value == "Unknown" or value is None:
        return "unknown"

    # Convert value to float if it's a string number
    if isinstance(value, str) and value.replace('.', '', 1).isdigit():
        value = float(value)

    # If value is not numeric after conversion attempts, return unknown
    if not isinstance(value, (int, float)):
        return "unknown"

    classifications = {
        'total_chol': [
            (200, 'optimal'), (240, 'borderline'), (float('inf'), 'high')],
        'ldl': [
            (100, 'optimal'), (130, 'near optimal'), (160, 'borderline'),
            (190, 'high'), (float('inf'), 'very high')],
        'hdl_male': [
            (40, 'low'), (46, 'borderline'), (60, 'optimal'), (float('inf'), 'high')],
        'hdl_female': [
            (50, 'low'), (56, 'borderline'), (60, 'optimal'), (float('inf'), 'high')],
        'triglycerides': [
            (150, 'optimal'), (200, 'borderline'), (500, 'high'),
            (float('inf'), 'very high')],
        'non_hdl': [
            (130, 'optimal'), (160, 'borderline'), (190, 'high'),
            (float('inf'), 'very high')],
        'vldl': [
            (30, 'optimal'), (40, 'borderline'), (float('inf'), 'high')]
    }

    # Handle HDL based on gender
    if component == 'hdl':
        if sex == 'Male':
            component = 'hdl_male'
        elif sex == 'Female':
            component = 'hdl_female'
        else:
            # Default to male if gender not specified
            component = 'hdl_male'

    for threshold, label in classifications[component]:
        if value < threshold:
            return label
    return 'unknown'

def calculate_ascvd_risk(data):
    """Simplified ASCVD risk calculation (in real app, use proper formula)"""
    score = 0
    if data['age'] > 45: score += 1
    if data['smoker'] in ['Occasional smoker', 'Regular smoker', 'Heavy smoker']: score += 1
    if data['hypertension'] == 'Yes': score += 1
    if data['diabetes'] in ['Yes, diabetic', 'Yes, pre-diabetic/borderline diabetic']: score += 1
    if data['family_history'] in ['Yes, in immediate family (parents or siblings)',
                             'Yes, in extended family (grandparents, uncles, aunts)']: score += 1

    if 'ldl' in data and data['ldl'] > 160: score += 2
    elif 'ldl' in data and data['ldl'] > 130: score += 1

    if score < 2: return 'Low'
    elif 2 <= score < 4: return 'Borderline'
    elif 4 <= score < 6: return 'Intermediate'
    else: return 'High'

def generate_diet_plan(data, analysis, ascvd_risk):
    """Generate a personalized diet plan based on lipid profile"""
    diet_plan = {
        "title": "Personalized Heart-Healthy Diet Plan",
        "overview": "",
        "recommended_foods": [],
        "foods_to_limit": [],
        "meal_suggestions": {
            "breakfast": [],
            "lunch": [],
            "dinner": [],
            "snacks": []
        }
    }

    # Base overview on risk level
    if ascvd_risk == 'Low':
        diet_plan["overview"] = "Your lipid profile suggests a low cardiovascular risk. This diet plan focuses on maintaining your heart health while supporting overall wellness."
    elif ascvd_risk == 'Borderline':
        diet_plan["overview"] = "Your lipid profile shows borderline cardiovascular risk. This diet plan aims to improve your lipid levels and reduce your risk factors."
    elif ascvd_risk == 'Intermediate':
        diet_plan["overview"] = "Your lipid profile indicates moderate cardiovascular risk. This diet plan is designed to significantly improve your lipid levels and reduce your risk of heart disease."
    else:  # High risk
        diet_plan["overview"] = "Your lipid profile shows elevated cardiovascular risk. This diet plan focuses on therapeutic changes to improve your lipid levels and heart health."

    # Base recommended foods (for everyone)
    diet_plan["recommended_foods"] = [
        "Fatty fish (salmon, mackerel, sardines) - rich in omega-3 fatty acids",
        "Nuts and seeds (walnuts, flaxseeds, chia seeds)",
        "Olive oil and avocados - sources of monounsaturated fats",
        "Colorful vegetables and fruits - high in antioxidants and fiber",
        "Whole grains (oats, quinoa, brown rice) - good sources of soluble fiber",
        "Legumes (beans, lentils, chickpeas) - plant protein with cholesterol-lowering effects",
        "Lean proteins (skinless poultry, tofu)"
    ]

    # Base foods to limit (for everyone)
    diet_plan["foods_to_limit"] = [
        "Processed meats (bacon, sausage, deli meats)",
        "Fried foods and fast food",
        "Baked goods made with trans fats",
        "Sugary beverages and excessive added sugars",
        "Full-fat dairy products"
    ]

    # Customize based on specific lipid abnormalities

    # LDL-specific recommendations
    if 'ldl' in analysis and analysis['ldl'] in ['high', 'very high']:
        diet_plan["recommended_foods"].extend([
            "Plant sterols/stanols (fortified foods)",
            "Barley, oats and other soluble fiber sources",
            "Soy protein (tofu, edamame, tempeh)"
        ])
        diet_plan["foods_to_limit"].extend([
            "Red meat - limit to once per week",
            "Coconut oil and palm oil - high in saturated fats",
            "Egg yolks - limit to 3-4 per week"
        ])

    # HDL-specific recommendations
    if 'hdl' in analysis and analysis['hdl'] == 'low':
        diet_plan["recommended_foods"].extend([
            "Extra virgin olive oil - 2-3 tablespoons daily",
            "Purple grapes and berries - rich in antioxidants",
            "Fatty fish - aim for 2-3 servings weekly"
        ])

    # Triglycerides-specific recommendations
    if 'triglycerides' in analysis and analysis['triglycerides'] in ['high', 'very high']:
        diet_plan["recommended_foods"].extend([
            "Omega-3 rich foods (flaxseeds, walnuts, fatty fish)",
            "High-fiber vegetables and low-glycemic fruits"
        ])
        diet_plan["foods_to_limit"].extend([
            "Alcohol - limit significantly or avoid completely",
            "Refined carbohydrates (white bread, pasta, rice)",
            "Sugary foods and beverages",
            "Fruit juices and dried fruits"
        ])

    # Meal suggestions
    # Breakfast options
    diet_plan["meal_suggestions"]["breakfast"] = [
        "Steel-cut oatmeal with berries, walnuts, and a sprinkle of cinnamon",
        "Greek yogurt parfait with fresh fruit and ground flaxseeds",
        "Vegetable omelet with 1-2 eggs or egg whites, spinach, tomatoes, and avocado",
        "Whole grain toast with avocado and smoked salmon",
        "Smoothie with berries, banana, plant-based protein, and ground flaxseeds"
    ]

    # Lunch options
    diet_plan["meal_suggestions"]["lunch"] = [
        "Mediterranean salad with leafy greens, chickpeas, olive oil, and a small amount of feta",
        "Lentil soup with a side of mixed greens",
        "Quinoa bowl with roasted vegetables and grilled chicken or tofu",
        "Whole grain wrap with hummus, vegetables, and lean protein",
        "Tuna salad (made with olive oil instead of mayo) on whole grain bread"
    ]

    # Dinner options
    diet_plan["meal_suggestions"]["dinner"] = [
        "Baked or grilled salmon with roasted vegetables and quinoa",
        "Stir-fry with tofu or chicken, plenty of vegetables, and brown rice",
        "Mediterranean-style vegetable and bean stew with a small portion of whole grain bread",
        "Grilled chicken or fish with a large salad and sweet potato",
        "Vegetable and lentil curry with a small portion of brown rice"
    ]

    # Snack options
    diet_plan["meal_suggestions"]["snacks"] = [
        "Small handful of unsalted nuts",
        "Apple or pear with 1 tablespoon of almond butter",
        "Hummus with raw vegetables",
        "Greek yogurt with berries",
        "Edamame beans"
    ]

    # Adjust meal suggestions based on risk level
    if ascvd_risk == 'High':
        # More restrictive diet for high risk
        diet_plan["meal_suggestions"]["breakfast"].append("Overnight oats made with plant-based milk, chia seeds, and berries (no added sugar)")
        diet_plan["meal_suggestions"]["lunch"].append("Large salad with beans, seeds, and olive oil-based dressing (no cheese)")
        diet_plan["meal_suggestions"]["dinner"].append("Completely plant-based dinner with legumes, whole grains, and plenty of vegetables")

    return diet_plan

def generate_cardiac_health_plan(data, analysis, ascvd_risk):
    """Generate a cardiac health plan based on lipid profile and risk assessment"""
    plan = {
        "title": "Personalized Cardiac Health Plan",
        "overview": "",
        "monitoring_recommendations": [],
        "lifestyle_changes": [],
        "follow_up": "",
        "potential_treatments": []
    }

    # Base overview on risk level
    if ascvd_risk == 'Low':
        plan["overview"] = "Your current cardiac risk is low. This plan focuses on maintaining your heart health and preventing future issues."
        plan["follow_up"] = "Consider having your lipid profile checked annually."
    elif ascvd_risk == 'Borderline':
        plan["overview"] = "Your cardiac risk is borderline. This plan aims to reduce your risk factors and improve your heart health."
        plan["follow_up"] = "Consider having your lipid profile checked every 6-12 months."
    elif ascvd_risk == 'Intermediate':
        plan["overview"] = "Your cardiac risk is moderate. This plan is designed to significantly reduce your risk of cardiovascular events."
        plan["follow_up"] = "Consider having your lipid profile checked every 6 months and discussing additional cardiac testing with your doctor."
    else:  # High risk
        plan["overview"] = "Your cardiac risk is elevated. This plan focuses on aggressive risk reduction and close monitoring."
        plan["follow_up"] = "Consider having your lipid profile checked every 3-6 months and discussing comprehensive cardiac evaluation with your doctor."

    # Base monitoring recommendations
    plan["monitoring_recommendations"] = [
        "Regular blood pressure monitoring",
        "Periodic lipid profile testing",
        "Blood glucose monitoring"
    ]

    # Base lifestyle changes
    plan["lifestyle_changes"] = [
        "Maintain a heart-healthy diet (see diet plan for details)",
        "Aim for at least 150 minutes of moderate exercise weekly",
        "Maintain a healthy weight",
        "Manage stress through mindfulness, meditation, or other techniques",
        "Ensure adequate sleep (7-8 hours nightly)"
    ]

    # Customize based on risk level and specific lipid abnormalities
    if ascvd_risk == 'High':
        plan["monitoring_recommendations"].extend([
            "Consider a cardiac calcium score test",
            "Discuss stress test or other cardiac imaging with your doctor",
            "More frequent blood pressure monitoring (consider home monitoring)"
        ])
        plan["potential_treatments"].extend([
            "Discuss statin therapy with your healthcare provider",
            "Consider aspirin therapy if recommended by your doctor",
            "Evaluate need for blood pressure medication if hypertensive"
        ])
    elif ascvd_risk == 'Intermediate':
        plan["monitoring_recommendations"].append("Consider additional risk assessment tests like high-sensitivity CRP")
        plan["potential_treatments"].append("Discuss whether preventive medications would be beneficial")

    # Add smoking cessation if applicable
    if data.get('smoker') == 'Yes':
        plan["lifestyle_changes"].insert(0, "Smoking cessation - highest priority for heart health")
        plan["potential_treatments"].append("Consider smoking cessation aids or programs")

    # Add specific recommendations based on lipid abnormalities
    if 'ldl' in analysis and analysis['ldl'] in ['high', 'very high']:
        plan["monitoring_recommendations"].append("More frequent LDL monitoring")
        if ascvd_risk in ['Intermediate', 'High']:
            plan["potential_treatments"].append("Consider LDL-lowering medications (statins, PCSK9 inhibitors)")

    if 'triglycerides' in analysis and analysis['triglycerides'] in ['high', 'very high']:
        plan["monitoring_recommendations"].append("Monitor triglyceride levels more frequently")
        plan["lifestyle_changes"].append("Limit alcohol consumption significantly or avoid completely")
        if analysis['triglycerides'] == 'very high':
            plan["potential_treatments"].append("Consider triglyceride-lowering medications if levels remain elevated despite lifestyle changes")

    return plan

def generate_recommendations(data, analysis, ascvd_risk):
    """Generate personalized recommendations"""
    recs = []

    # General recommendations
    recs.append("Maintain a heart-healthy diet (Mediterranean or DASH diet recommended)")
    recs.append("Aim for at least 150 minutes of moderate exercise weekly")
    recs.append("Maintain a healthy weight or work toward weight loss if overweight")

    # Non-HDL specific
    if 'non_hdl' in analysis:
        if analysis['non_hdl'] in ['high', 'very high']:
            recs.append("Focus on reducing LDL and triglyceride levels to lower Non-HDL cholesterol")
            recs.append("Increase consumption of plant sterols/stanols found in vegetables, fruits, and fortified foods")

    # VLDL specific
    if 'vldl' in analysis:
        if analysis['vldl'] in ['high']:
            recs.append("High VLDL suggests need to reduce simple carbohydrates and alcohol intake")
            recs.append("Consider increasing omega-3 fatty acid consumption through fatty fish or supplements")
            recs.append("Focus on complex carbohydrates with low glycemic index")

    # LDL-specific
    if 'ldl' in analysis:
        if analysis['ldl'] in ['high', 'very high']:
            recs.append("Reduce saturated fats (red meat, full-fat dairy) and increase soluble fiber (oats, beans, fruits)")
            recs.append("Consider adding plant sterols/stanols to your diet through fortified foods")
            if ascvd_risk in ['Intermediate', 'High']:
                recs.append("Consult your doctor about statin therapy or other cholesterol-lowering medications")
            recs.append("Increase consumption of foods with natural cholesterol-lowering effects like oats, barley, and legumes")

    # HDL-specific
    if 'hdl' in analysis and analysis['hdl'] == 'low':
        recs.append("Increase physical activity - aim for 30 minutes daily to raise HDL levels")
        recs.append("Include healthy fat sources like olive oil, avocados, and fatty fish in your diet")
        recs.append("Consider moderate alcohol consumption if appropriate (one drink daily for women, up to two for men)")
        recs.append("Quit smoking if applicable - smoking lowers HDL levels")

    # Triglycerides-specific
    if 'triglycerides' in analysis and analysis['triglycerides'] in ['high', 'very high']:
        recs.append("Significantly reduce intake of refined carbohydrates, sugars, and sweetened beverages")
        recs.append("Limit or avoid alcohol consumption")
        recs.append("Increase omega-3 fatty acid intake through fatty fish or supplements")
        recs.append("Focus on high-fiber, low-glycemic index foods")
        if analysis['triglycerides'] == 'very high':
            recs.append("Discuss triglyceride-lowering medications with your healthcare provider")

    # Risk-specific
    if ascvd_risk == 'High':
        recs.append("Schedule a consultation with a cardiologist to discuss comprehensive risk management")
        recs.append("Consider additional cardiac testing such as calcium scoring or stress testing")
        recs.append("Discuss aspirin therapy with your healthcare provider if appropriate")
    elif ascvd_risk == 'Intermediate':
        recs.append("Consider more frequent lipid monitoring (every 3-6 months)")
        recs.append("Discuss additional risk assessment tests with your healthcare provider")
    elif ascvd_risk == 'Borderline':
        recs.append("Monitor lipid levels every 6-12 months")
        recs.append("Focus on lifestyle modifications to prevent progression to higher risk")

    return recs

def plot_lipid_profile(data, save_path=None):
    """Create visualization of lipid profile"""
    fig, ax = plt.subplots(figsize=(12, 6))

    components = ['Total Cholesterol', 'LDL', 'HDL', 'Triglycerides', 'Non-HDL', 'VLDL']
    values = [data.get('total_chol', 0), data.get('ldl', 0),
              data.get('hdl', 0), data.get('triglycerides', 0),
              data.get('non_hdl', 0), data.get('vldl', 0)]

    # Ideal values
    ideals = [200, 100, 60, 150, 130, 30]

    # Plot
    x = range(len(components))
    ax.bar(x, values, width=0.4, label='Your Values')
    ax.bar([i + 0.4 for i in x], ideals, width=0.4, label='Ideal Values')

    ax.set_xticks([i + 0.2 for i in x])
    ax.set_xticklabels(components, rotation=45, ha='right')
    ax.set_ylabel('mg/dL')
    ax.set_title('Your Lipid Profile vs Ideal Values')
    ax.legend()

    if save_path:
        plt.savefig(save_path, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    else:
        plt.show()

    return fig

def print_results(data, analysis, ascvd_risk, recommendations):
    """Print analysis results to console"""
    print("\n==== 📊 Your Lipid Profile Analysis ====")

    # Print metrics
    print("\nLipid Values:")
    metrics = [
        ("Total Cholesterol", data.get('total_chol', 0), 'total_chol'),
        ("LDL Cholesterol", data.get('ldl', 0), 'ldl'),
        ("HDL Cholesterol", data.get('hdl', 0), 'hdl'),
        ("Triglycerides", data.get('triglycerides', 0), 'triglycerides'),
        ("Non-HDL Cholesterol", data.get('non_hdl', 0), 'non_hdl'),
        ("VLDL Cholesterol", data.get('vldl', 0), 'vldl')
    ]

    for label, value, key in metrics:
        if key in analysis:
            print(f"{label}: {value} mg/dL - {analysis.get(key, '')}")
        else:
            print(f"{label}: {value} mg/dL")

    # Show ASCVD risk
    print(f"\n🫀 Cardiovascular Risk: {ascvd_risk}")

    # Show recommendations
    print("\n💡 Personalized Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")

    # Show reference ranges
    print("\n📋 Reference Ranges:")
    for component, ranges in REF_RANGES.items():
        print(f"- {component}:")
        for level, value in ranges.items():
            print(f"  {level}: {value}")


def analyze_lipid_profile(data):
    """Analyze lipid profile and generate recommendations"""
    # Calculate Non-HDL if not provided but total and HDL are available
    if 'non_hdl' not in data and 'total_chol' in data and 'hdl' in data:
        data['non_hdl'] = data['total_chol'] - data['hdl']

    # Calculate VLDL if not provided but triglycerides are available
    if 'vldl' not in data and 'triglycerides' in data:
        data['vldl'] = data['triglycerides'] // 5  # Common estimation method

    # Classify each component
    analysis = {}
    for key in ['total_chol', 'ldl', 'hdl', 'triglycerides', 'non_hdl', 'vldl']:
        if key in data:
            # Special handling for HDL which needs gender information
            if key == 'hdl':
                # Check if gender is available in data, otherwise provide a default
                sex = data.get('sex', None)  # Assuming gender is stored in data dictionary
                analysis[key] = classify_component(key, data[key], sex=sex)
            else:
                analysis[key] = classify_component(key, data[key])

    # Generate ASCVD risk score
    ascvd_risk = calculate_ascvd_risk(data)

    # Generate recommendations
    recommendations = generate_recommendations(data, analysis, ascvd_risk)

    # Generate diet plan
    diet_plan = generate_diet_plan(data, analysis, ascvd_risk)

    # Generate cardiac health plan
    cardiac_health_plan = generate_cardiac_health_plan(data, analysis, ascvd_risk)

    # Create a doctor-like summary
    doctor_summary = "Hi there! I've reviewed your lipid profile results, and here's what I'm seeing: "

    # Add a summary based on risk level
    if ascvd_risk == "Low":
        doctor_summary += "Good news! Your cardiovascular risk based on these lipid values appears to be low. "
    elif ascvd_risk == "Borderline":
        doctor_summary += "I'm seeing a borderline level of cardiovascular risk based on your lipid profile. "
    elif ascvd_risk == "Intermediate":
        doctor_summary += "I'm seeing a moderate level of cardiovascular risk based on your lipid profile. "
    elif ascvd_risk == "High":
        doctor_summary += "I'm noticing an elevated cardiovascular risk based on your lipid profile that we should address. "

    # Add a note about the importance of lipid profile
    doctor_summary += "Your lipid profile is an important indicator of heart health and can help us prevent cardiovascular issues. "

    # Add specific notes about concerning values
    concerns = []
    for component, level in analysis.items():
        if level in ["high", "very high", "low"] and component != "hdl":  # For HDL, "high" is actually good
            component_name = component.replace('_', ' ').title()
            if component == "hdl" and level == "low":
                concerns.append(f"your {component_name} is lower than optimal")
            elif component != "hdl" and level in ["high", "very high"]:
                concerns.append(f"your {component_name} is higher than optimal")

    if concerns:
        doctor_summary += f"I'm particularly noting that {', '.join(concerns)}. "
    else:
        doctor_summary += "Most of your values are within or close to the optimal ranges. "

    # Add explanation for each lipid parameter
    lipid_explanations = {
        "total_chol": "Total Cholesterol is the sum of all cholesterol in your blood. It includes both 'good' (HDL) and 'bad' (LDL) cholesterol.",
        "ldl": "LDL (Low-Density Lipoprotein) is often called 'bad' cholesterol because high levels can lead to plaque buildup in your arteries.",
        "hdl": "HDL (High-Density Lipoprotein) is known as 'good' cholesterol because it helps remove other forms of cholesterol from your bloodstream.",
        "triglycerides": "Triglycerides are a type of fat in your blood. High levels can increase your risk of heart disease, especially in combination with high LDL or low HDL.",
        "non_hdl": "Non-HDL Cholesterol includes all 'bad' cholesterol types and is a stronger predictor of cardiovascular risk than LDL alone.",
        "vldl": "VLDL (Very Low-Density Lipoprotein) carries triglycerides in your blood and can contribute to plaque buildup in arteries."
    }

    # Create detailed explanations for each parameter
    parameter_explanations = []
    for key in ['total_chol', 'ldl', 'hdl', 'triglycerides', 'non_hdl', 'vldl']:
        if key in data and key in analysis:
            value = data[key]
            status = analysis[key]
            explanation = lipid_explanations.get(key, "")

            # Format the parameter name
            param_name = key.replace('_', ' ').title()

            # Add status-specific explanation
            if key == 'hdl':
                if status == 'low':
                    status_explanation = "Your level is lower than optimal, which may increase cardiovascular risk."
                elif status == 'optimal' or status == 'high':
                    status_explanation = "Your level is optimal, which helps protect against heart disease."
                else:
                    status_explanation = "Your level is borderline and could be improved."
            else:  # For all other parameters, high is bad
                if status in ['high', 'very high']:
                    status_explanation = "Your level is higher than optimal, which may increase cardiovascular risk."
                elif status == 'optimal':
                    status_explanation = "Your level is optimal, which is good for heart health."
                else:
                    status_explanation = "Your level is borderline and could be improved."

            # Combine into a complete explanation
            parameter_explanations.append({
                "parameter": param_name,
                "value": value,
                "status": status,
                "explanation": explanation,
                "status_explanation": status_explanation
            })

    # Add device recommendation prompt (following agent_app pattern)
    device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your lipid profile results? (Yes/No)**"

    return {
        "classification": analysis,
        "ascvd_risk": ascvd_risk,
        "recommendations": recommendations,
        "ref_ranges": REF_RANGES,
        "doctor_summary": doctor_summary,
        "parameter_explanations": parameter_explanations,
        "diet_plan": diet_plan,
        "cardiac_health_plan": cardiac_health_plan,
        "device_recommendation_prompt": device_recommendation_prompt,
        "test_type": "lipid_profile"
    }



# Optional CLI runner
def main():
    """Main function without UI components"""
    print("🩸 AI Lipid Profile Analyzer")
    print("============================")

    # Initialize data dictionary with defaults
    data = {
        "age": int(input("Age: ")),
        "sex": input("Sex (Male/Female): "),
        "smoker": input("Smoker? (Yes/No): "),
        "hypertension": input("Hypertension? (Yes/No): "),
        "diabetes": input("Diabetes? (Yes/No): "),
        "family_history": input("Family history of heart disease? (Yes/No): "),
        "total_chol": int(input("Total Cholesterol: ")),
        "ldl": int(input("LDL: ")),
        "hdl": int(input("HDL: ")),
        "triglycerides": int(input("Triglycerides: "))
    }

    # Manual Input
    print("\nManual Input (press Enter to keep current values)")

    # Demographic info
    print("\nDemographic Information:")
    age_input = input(f"Age [{data['age']}]: ")
    if age_input.strip():
        data['age'] = int(age_input)

    sex_input = input(f"Sex (Male/Female) [{data['sex']}]: ")
    if sex_input.strip():
        data['sex'] = sex_input

    # Risk factors
    print("\nRisk Factors:")
    print("Smoker options: 1) Non-smoker  2) Occasional smoker  3) Regular smoker  4) Heavy smoker")
    smoker_input = input(f"Select smoker status (1-4) [1]: ")
    if smoker_input.strip():
        smoker_options = ["Non-smoker", "Occasional smoker", "Regular smoker", "Heavy smoker"]
        try:
            data['smoker'] = smoker_options[int(smoker_input) - 1]
        except (ValueError, IndexError):
            print("Invalid input, using default: Non-smoker")
            data['smoker'] = "Non-smoker"

    hypertension_input = input(f"Hypertension (Yes/No) [{data['hypertension']}]: ")
    if hypertension_input.strip():
        data['hypertension'] = hypertension_input

    print("Diabetes options: 1) No  2) Yes, diabetic  3) Yes, pre-diabetic  4) I don't know")
    diabetes_input = input("Select diabetes status (1-4) [1]: ")
    if diabetes_input.strip():
        diabetes_options = ["No", "Yes, diabetic", "Yes, pre-diabetic/borderline diabetic", "I don't know"]
        try:
            data['diabetes'] = diabetes_options[int(diabetes_input) - 1]
        except (ValueError, IndexError):
            print("Invalid input, using default: No")
            data['diabetes'] = "No"

    print("Family history options:")
    print("1) No family history")
    print("2) Yes, in immediate family (parents or siblings)")
    print("3) Yes, in extended family (grandparents, uncles, aunts)")
    print("4) Unsure")

    fh_input = input("Select family history option (1-4) [1]: ")
    if fh_input.strip():
        fh_options = [
            "No family history",
            "Yes, in immediate family (parents or siblings)",
            "Yes, in extended family (grandparents, uncles, aunts)",
            "Unsure"
        ]
        try:
            data['family_history'] = fh_options[int(fh_input) - 1]
        except (ValueError, IndexError):
            print("Invalid input, using default: No family history")
            data['family_history'] = "No family history"

    # Lipid profile values
    print("\nLipid Profile Values:")

    # Lipid inputs with current values displayed
    total_chol_input = input(f"Total Cholesterol (mg/dL) [{data.get('total_chol', '')}]: ")
    if total_chol_input.strip():
        data['total_chol'] = int(total_chol_input)

    ldl_input = input(f"LDL Cholesterol (mg/dL) [{data.get('ldl', '')}]: ")
    if ldl_input.strip():
        data['ldl'] = int(ldl_input)

    hdl_input = input(f"HDL Cholesterol (mg/dL) [{data.get('hdl', '')}]: ")
    if hdl_input.strip():
        data['hdl'] = int(hdl_input)

    triglycerides_input = input(f"Triglycerides (mg/dL) [{data.get('triglycerides', '')}]: ")
    if triglycerides_input.strip():
        data['triglycerides'] = int(triglycerides_input)

    non_hdl_input = input(f"Non-HDL Cholesterol (mg/dL) [{data.get('non_hdl', '')}]: ")
    if non_hdl_input.strip():
        data['non_hdl'] = int(non_hdl_input)

    vldl_input = input(f"VLDL Cholesterol (mg/dL) [{data.get('vldl', '')}]: ")
    if vldl_input.strip():
        data['vldl'] = int(vldl_input)

    # Calculate Non-HDL if not provided but total and HDL are available
    if 'non_hdl' not in data and 'total_chol' in data and 'hdl' in data:
        data['non_hdl'] = data['total_chol'] - data['hdl']

    # Calculate VLDL if not provided but triglycerides are available
    if 'vldl' not in data and 'triglycerides' in data:
        data['vldl'] = data['triglycerides'] // 5  # Common estimation method

    # Wait for user to request analysis
    input("\nPress Enter to analyze your lipid profile...")

    # Analyze the data
    print("\nAnalyzing lipid profile...")
    analysis, ascvd_risk, recommendations = analyze_lipid_profile(data)

    # Display results
    print_results(data, analysis, ascvd_risk, recommendations)

    # Generate visualization
    viz_input = input("\nView visualization? (yes/no): ")
    if viz_input.lower().startswith('yes'):
        save_viz = input("Save visualization to file? (yes/no): ")
        if save_viz.lower().startswith('yes'):
            save_path = input("Enter file path (default: lipid_profile.png): ") or "lipid_profile.png"
            plot_lipid_profile(data, save_path=save_path)
        else:
            print("Displaying visualization...")
            plot_lipid_profile(data)

if __name__ == "__main__":
    main()