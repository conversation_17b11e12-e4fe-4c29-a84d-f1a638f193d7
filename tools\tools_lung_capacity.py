import json
from langchain.tools import Tool
from typing import Dict, List, Any, Optional, Union
import logging
import sys
import os

# Import peak flow calculator functions
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from peak_flow_calculator import calculate_expected_fev1, calculate_pef
except ImportError:
    # Define fallback functions if import fails
    def calculate_expected_fev1(age, height_cm, race):
        race_factors = {
            "asian": 0.93, "latino": 0.93, "hispanic": 0.93,
            "african": 0.87, "african american": 0.87,
            "caucasian": 1, "middle eastern": 1,
        }
        race_key = race.strip().lower()
        factor = race_factors.get(race_key, 1)
        expected_fev1 = factor * 1.08 * ((0.0395 * height_cm) - (0.025 * age) - 2.6)
        expected_fvc = factor * 1.15 * ((0.0443 * height_cm) - (0.026 * age) - 2.89)
        return expected_fev1, expected_fvc

    def calculate_pef(age, height_cm, gender):
        height_m = height_cm / 100
        if age < 18:
            pef = (height_cm - 100) * 5 + 100
        else:
            gender_key = gender.strip().lower()
            if gender_key == "male":
                pef = (((height_m * 5.48) + 1.58) - (age * 0.041)) * 60
            elif gender_key == "female":
                pef = (((height_m * 3.72) + 2.24) - (age * 0.03)) * 60
            else:
                pef = (((height_m * 4.6) + 1.9) - (age * 0.035)) * 60
        return pef

def analyze_lung_capacity(data_json: str) -> str:
    """
    Analyzes spirometry data to assess lung capacity and respiratory health.
    Uses pattern recognition and rule-based triggers to identify potential respiratory issues.

    Args:
        data_json: JSON string containing spirometry data and patient information

    Returns:
        JSON string with analysis results, risk assessment, and recommendations
    """
    try:
        # Parse input data
        data = json.loads(data_json)

        # Extract spirometry parameters
        spirometry_data = data.get("data", {})

        # Initialize results
        analysis_results = []
        risk_factors = []
        recommendations = []
        respiratory_conditions = []

        # Define normal ranges for spirometry parameters
        normal_ranges = {
            "FEV1": {"range": (80, 120), "unit": "%"},  # Forced Expiratory Volume in 1 second (percent predicted)
            "FVC": {"range": (80, 120), "unit": "%"},   # Forced Vital Capacity (percent predicted)
            "FEV1_FVC_ratio": {"range": (0.7, 1.0), "unit": "ratio"},  # FEV1/FVC ratio
            "PEF": {"range": (80, 120), "unit": "%"},   # Peak Expiratory Flow (percent predicted)
            "FEF25_75": {"range": (60, 120), "unit": "%"},  # Forced Expiratory Flow at 25-75% (percent predicted)
            "TLC": {"range": (80, 120), "unit": "%"},   # Total Lung Capacity (percent predicted)
            "RV": {"range": (75, 120), "unit": "%"},    # Residual Volume (percent predicted)
            "DLCO": {"range": (80, 120), "unit": "%"},  # Diffusing capacity for carbon monoxide (percent predicted)
        }

        # Extract patient information
        age = spirometry_data.get("Age", 0)
        sex = spirometry_data.get("Sex", "")
        gender = "male" if sex.lower() == "male" else "female"  # Normalize for peak flow calculation
        smoking_status = spirometry_data.get("Smoking_Status", "")
        height = spirometry_data.get("Height", 170)  # Default to 170cm if not provided
        weight = spirometry_data.get("Weight", 0)
        race = spirometry_data.get("Race", "caucasian")  # Default to caucasian if not provided

        # Extract key spirometry values and handle "Unknown" values
        fev1 = spirometry_data.get("FEV1", None)  # As percentage of predicted
        fev1 = None if fev1 == "Unknown" else fev1

        fvc = spirometry_data.get("FVC", None)    # As percentage of predicted
        fvc = None if fvc == "Unknown" else fvc

        fev1_fvc_ratio = spirometry_data.get("FEV1_FVC_ratio", None)
        fev1_fvc_ratio = None if fev1_fvc_ratio == "Unknown" else fev1_fvc_ratio

        pef = spirometry_data.get("PEF", None)    # As percentage of predicted
        pef = None if pef == "Unknown" else pef

        fef25_75 = spirometry_data.get("FEF25_75", None)
        fef25_75 = None if fef25_75 == "Unknown" else fef25_75

        # Calculate expected values using peak flow calculator if height and age are provided
        if height > 0 and age > 0:
            try:
                expected_fev1, expected_fvc = calculate_expected_fev1(age, height, race)
                expected_pef = calculate_pef(age, height, gender)

                # If values are provided as percentages, convert to absolute values
                if fev1 is not None and fev1 > 0 and fev1 <= 200:
                    absolute_fev1 = (fev1 / 100) * expected_fev1
                    analysis_results.append(f"FEV1 is {fev1}% of predicted value ({absolute_fev1:.2f}L, expected {expected_fev1:.2f}L)")

                if fvc is not None and fvc > 0 and fvc <= 200:
                    absolute_fvc = (fvc / 100) * expected_fvc
                    analysis_results.append(f"FVC is {fvc}% of predicted value ({absolute_fvc:.2f}L, expected {expected_fvc:.2f}L)")

                if pef is not None and pef > 0 and pef <= 200:
                    absolute_pef = (pef / 100) * expected_pef
                    analysis_results.append(f"PEF is {pef}% of predicted value ({absolute_pef:.2f}L/min, expected {expected_pef:.2f}L/min)")

                # Add expected values to the result
                analysis_results.append(f"Based on your age, height, and demographics, your expected values are: FEV1 {expected_fev1:.2f}L, FVC {expected_fvc:.2f}L, PEF {expected_pef:.2f}L/min")
            except Exception as e:
                logging.warning(f"Error calculating expected values: {str(e)}")

        # For backward compatibility, keep these variables
        tlc = spirometry_data.get("TLC", None)
        rv = spirometry_data.get("RV", None)
        dlco = spirometry_data.get("DLCO", None)

        # Track missing parameters
        missing_parameters = []
        for param in ["FEV1", "FVC", "FEV1_FVC_ratio"]:
            if param not in spirometry_data or spirometry_data[param] is None:
                missing_parameters.append(param)

        # Analyze each parameter if available
        if fev1 is not None:
            if fev1 < 80:
                severity = "mild" if fev1 >= 70 else "moderate" if fev1 >= 60 else "moderately severe" if fev1 >= 50 else "severe" if fev1 >= 35 else "very severe"
                analysis_results.append(f"FEV1 is {fev1}% of predicted value, indicating {severity} airflow limitation.")
                risk_factors.append("Reduced FEV1")
            else:
                analysis_results.append(f"FEV1 is {fev1}% of predicted value, which is within normal range.")

        if fvc is not None:
            if fvc < 80:
                severity = "mild" if fvc >= 70 else "moderate" if fvc >= 60 else "severe" if fvc < 60 else ""
                analysis_results.append(f"FVC is {fvc}% of predicted value, indicating {severity} restrictive pattern.")
                risk_factors.append("Reduced FVC")
            else:
                analysis_results.append(f"FVC is {fvc}% of predicted value, which is within normal range.")

        if fev1_fvc_ratio is not None:
            if fev1_fvc_ratio < 0.7:
                analysis_results.append(f"FEV1/FVC ratio is {fev1_fvc_ratio}, indicating obstructive lung disease.")
                risk_factors.append("Low FEV1/FVC ratio")
                respiratory_conditions.append("Obstructive lung disease")

                # Determine if likely COPD or asthma
                if smoking_status and ("current" in smoking_status.lower() or "former" in smoking_status.lower()):
                    respiratory_conditions.append("Possible COPD")
                    if age and age > 40:
                        analysis_results.append("Pattern is consistent with COPD given age and smoking history.")
                else:
                    respiratory_conditions.append("Possible asthma")
                    analysis_results.append("Pattern may be consistent with asthma.")
            elif fev1_fvc_ratio > 0.9 and fvc and fvc < 80:
                analysis_results.append(f"FEV1/FVC ratio is {fev1_fvc_ratio} with reduced FVC, suggesting restrictive lung disease.")
                respiratory_conditions.append("Possible restrictive lung disease")
            else:
                analysis_results.append(f"FEV1/FVC ratio is {fev1_fvc_ratio}, which is within normal range.")

        if pef is not None:
            if pef < 80:
                analysis_results.append(f"PEF is {pef}% of predicted value, indicating reduced peak flow.")
                risk_factors.append("Reduced PEF")
            else:
                analysis_results.append(f"PEF is {pef}% of predicted value, which is within normal range.")

        if fef25_75 is not None:
            if fef25_75 < 60:
                analysis_results.append(f"FEF25-75 is {fef25_75}% of predicted value, indicating small airway dysfunction.")
                risk_factors.append("Reduced FEF25-75")
                if fev1_fvc_ratio and fev1_fvc_ratio >= 0.7:
                    analysis_results.append("Normal FEV1/FVC ratio with reduced FEF25-75 may indicate early small airway disease.")
            else:
                analysis_results.append(f"FEF25-75 is {fef25_75}% of predicted value, which is within normal range.")

        # Determine overall respiratory risk level
        risk_level = "Low"
        if len(risk_factors) >= 3 or "Low FEV1/FVC ratio" in risk_factors:
            risk_level = "High"
        elif len(risk_factors) >= 1:
            risk_level = "Moderate"

        # Generate recommendations based on findings
        if "Obstructive lung disease" in respiratory_conditions:
            if "Possible COPD" in respiratory_conditions:
                recommendations.append("🫁 Consider pulmonary function testing with bronchodilator response to confirm COPD diagnosis.")
                recommendations.append("🚭 Smoking cessation is essential to prevent further lung damage.")
                recommendations.append("👨‍⚕️ Consult with a pulmonologist for comprehensive COPD management.")
            elif "Possible asthma" in respiratory_conditions:
                recommendations.append("🫁 Consider pulmonary function testing with bronchodilator response to confirm asthma diagnosis.")
                recommendations.append("🧪 Allergy testing may help identify triggers.")
                recommendations.append("👨‍⚕️ Consult with a pulmonologist or allergist for asthma management.")

        if "Possible restrictive lung disease" in respiratory_conditions:
            recommendations.append("🫁 Consider full pulmonary function testing including lung volumes and diffusion capacity.")
            recommendations.append("🩻 Chest imaging may be indicated to evaluate for interstitial lung disease.")
            recommendations.append("👨‍⚕️ Consult with a pulmonologist for further evaluation.")

        if risk_level == "Low" and not respiratory_conditions:
            recommendations.append("✅ Continue regular health maintenance and preventive care.")
            if smoking_status and "current" in smoking_status.lower():
                recommendations.append("🚭 Consider smoking cessation to maintain lung health.")

        # Add general recommendations for moderate to high risk
        if risk_level in ["Moderate", "High"]:
            recommendations.append("💨 Practice breathing exercises to improve lung function.")
            recommendations.append("🏃 Regular aerobic exercise can help improve respiratory capacity.")
            recommendations.append("😷 Avoid respiratory irritants and air pollution when possible.")

        # Determine confidence level based on available data
        confidence_level = "Low" if len(missing_parameters) >= 2 else "Moderate" if missing_parameters else "High"

        # Create a doctor-like summary
        doctor_summary = ""

        # Add a greeting and introduction
        doctor_summary += "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: "

        # Add a summary based on risk level
        if risk_level == "Low":
            doctor_summary += "Your lung function appears to be in good shape overall. "
        elif risk_level == "Moderate":
            doctor_summary += "I'm noticing some areas of concern in your lung function that we should keep an eye on. "
        elif risk_level == "High":
            doctor_summary += "I'm seeing some significant concerns with your lung function that we should address. "

        # Add a note about confidence
        if confidence_level == "High":
            doctor_summary += "The test provided comprehensive data, so I'm quite confident in this assessment."
        elif confidence_level == "Moderate":
            doctor_summary += "The test provided most of the key measurements, giving us a reasonably good picture of your lung health."
        elif confidence_level == "Low":
            doctor_summary += f"We're missing some measurements ({', '.join(missing_parameters)}), so this is a preliminary assessment."

        # Add information about potential conditions
        if respiratory_conditions and respiratory_conditions != ["No specific respiratory conditions identified"]:
            doctor_summary += " Based on these patterns, I'm seeing indicators that could be associated with "
            doctor_summary += ", ".join(respiratory_conditions) + "."
            doctor_summary += " Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."

        # Add device recommendation prompt (following agent_app pattern)
        device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your lung capacity results? (Yes/No)**"

        # Create final result
        result = {
            "analysis": analysis_results,
            "respiratory_risk_level": risk_level,
            "potential_conditions": respiratory_conditions if respiratory_conditions else ["No specific respiratory conditions identified"],
            "recommendations": recommendations,
            "confidence_level": confidence_level,
            "missing_parameters": missing_parameters,
            "doctor_summary": doctor_summary,
            "device_recommendation_prompt": device_recommendation_prompt,
            "test_type": "lung_capacity"
        }

        return json.dumps(result, indent=4)

    except Exception as e:
        logging.error(f"Error analyzing lung capacity: {str(e)}")
        return json.dumps({
            "error": f"Failed to analyze lung capacity: {str(e)}",
            "recommendations": ["⚠️ Unable to process spirometry data. Please consult a healthcare professional."]
        }, indent=4)

# Create the tool
lung_capacity_analyzer_tool = Tool(
    name="LungCapacityAnalyzer",
    func=analyze_lung_capacity,
    description="Analyzes spirometry data to assess lung capacity, identify respiratory risks like COPD/asthma, and provide respiratory health recommendations."
)
